import { APIROUTES } from "@/helpers/apiRoutes";
import { orpc } from "@/lib/orpc";
import { useMutation, useQueryClient } from "@tanstack/react-query";
import toast from "react-hot-toast";
import emotes from "../data/chatEmotes.json";
import { ChatMessage } from "../types/chat";

// Define type for the emotes
type EmotesRecord = Record<string, string>;

// Import.meta.glob type
interface ImportMetaGlob {
    [key: string]: {
        default: string;
    };
}

export const getEmoteSrc = (name: string): string | undefined => {
    const path = `/src/assets/icons/emotes/${name}`;
    const modules = import.meta.glob("/src/assets/icons/emotes/*", { eager: true }) as ImportMetaGlob;
    const mod = modules[path];
    return mod?.default;
};

export const getEmote = (name: string): string | undefined => {
    const emote = (emotes as EmotesRecord)[name];

    const path = `/src/assets/icons/emotes/${emote}`;
    const modules = import.meta.glob("/src/assets/icons/emotes/*", { eager: true }) as ImportMetaGlob;
    const mod = modules[path];
    return mod?.default;
};

export const countEmotesInMessage = (text: string): number => {
    const emojiRegexPattern = Object.keys(emotes)
        .map((key) => `:${key}`)
        .join("|");
    const regex = new RegExp(`(${emojiRegexPattern})`, "g");
    return (text.match(regex) || []).length;
};

// Custom hooks for chat moderation actions
export const useHideMessage = () => {
    const queryClient = useQueryClient();

    return useMutation(
        orpc.admin.hideSingleMessage.mutationOptions({
            onSuccess: () => {
                toast.success("Message hidden");
                // Invalidate chat history to refresh the UI
                queryClient.invalidateQueries({ queryKey: ["chat", "history"] });
            },
            onError: (error) => {
                console.error("Error hiding message:", error);
                toast.error(error.message || "Failed to hide message");
            },
        })
    );
};

export const useUnhideMessage = () => {
    const queryClient = useQueryClient();

    return useMutation(
        orpc.admin.unhideSingleMessage.mutationOptions({
            onSuccess: () => {
                toast.success("Message unhidden");
                // Invalidate chat history to refresh the UI
                queryClient.invalidateQueries({ queryKey: ["chat", "history"] });
            },
            onError: (error) => {
                console.error("Error unhiding message:", error);
                toast.error(error.message || "Failed to unhide message");
            },
        })
    );
};

export const useDeleteMessage = () => {
    const queryClient = useQueryClient();

    return useMutation(
        orpc.admin.deleteSingleMessage.mutationOptions({
            onSuccess: () => {
                toast.success("Message deleted");
                // Invalidate chat history to refresh the UI
                queryClient.invalidateQueries({ queryKey: ["chat", "history"] });
            },
            onError: (error) => {
                console.error("Error deleting message:", error);
                toast.error(error.message || "Failed to delete message");
            },
        })
    );
};

export const useChatBanUser = () => {
    const queryClient = useQueryClient();

    return useMutation(
        orpc.admin.chatBanUser.mutationOptions({
            onSuccess: (_, variables) => {
                const minutes = variables.timeMS / 60000;
                toast.success(`Chat banned user for ${minutes} minutes`);
                // Invalidate chat history to refresh the UI
                queryClient.invalidateQueries({ queryKey: ["chat", "history"] });
            },
            onError: (error) => {
                console.error("Error chat banning user:", error);
                toast.error(error.message || "Failed to chat ban user");
            },
        })
    );
};

// Helper functions for the components to use
export const hideMessage = (hideMessageMutation: any, id: string | number) => {
    const parsedID = parseInt(id.toString());
    hideMessageMutation.mutate({ messageId: parsedID });
};

export const unhideMessage = (unhideMessageMutation: any, id: string | number) => {
    const parsedID = parseInt(id.toString());
    unhideMessageMutation.mutate({ messageId: parsedID });
};

export const deleteMessage = (deleteMessageMutation: any, id: string | number) => {
    const parsedID = parseInt(id.toString());
    deleteMessageMutation.mutate({ messageId: parsedID });
};

export const timeoutUser = (chatBanMutation: any, msg: ChatMessage) => {
    const parsedID = parseInt(msg.userId?.toString() || "0");
    chatBanMutation.mutate({
        userId: parsedID,
        timeMS: 1200000, // 20 minutes
    });
};

export const chatBanUser = (chatBanMutation: any, msg: ChatMessage) => {
    const banLengthPrompt = window.prompt(`Enter Chatban length for #${msg?.userId} (minutes)`) as string | null;
    const banLength = parseInt(banLengthPrompt || "0") * 60000;

    if (banLength > 0) {
        const parsedID = parseInt(msg.userId?.toString() || "0");
        chatBanMutation.mutate({
            userId: parsedID,
            timeMS: banLength,
        });
    }
};

const zombieWords: Record<string, string> = {
    hello: "grrraah",
    hi: "braains",
    friend: "fleshling",
    help: "huuunger",
    run: "reee!",
    food: "meat",
    yes: "grrr",
    no: "nooo",
    please: "pleeease",
    thank: "thaaank",
    you: "yoouu",
    good: "gooood",
    bad: "baaad",
};

const zombiePhrases: string[] = ["...grrraah", "...braains", "...huunger"];

export function zombifyMessage(message: string): string {
    // Split the message into words
    const words = message.split(" ");

    // Iterate through words and replace with zombie words with 25% chance
    const zombifiedWords = words.map((word) => {
        if (Math.random() < 0.7) {
            return zombieWords[word.toLowerCase()] || word;
        }
        return word;
    });

    // Join the words back into a single string
    let zombifiedMessage = zombifiedWords.join(" ");

    // 25% chance to add a zombie phrase at the start or end
    if (Math.random() < 0.25) {
        const zombiePhrase = zombiePhrases[Math.floor(Math.random() * zombiePhrases.length)];
        if (Math.random() < 0.5) {
            // Add to the start
            zombifiedMessage = zombiePhrase + " " + zombifiedMessage;
        } else {
            // Add to the end
            zombifiedMessage = zombifiedMessage + " " + zombiePhrase;
        }
    }

    return zombifiedMessage;
}
