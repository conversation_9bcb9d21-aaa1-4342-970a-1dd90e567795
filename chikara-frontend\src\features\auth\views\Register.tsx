import Button from "@/components/Buttons/Button";
import RegistrationDisabled from "@/features/auth/components/RegistrationDisabled";
import useGameConfig from "@/hooks/useGameConfig";
import { signIn } from "@/lib/auth-client";
import posthog from "posthog-js";
import { useEffect, useState } from "react";
import { toast } from "react-hot-toast";
import { Link, useNavigate, useSearchParams } from "react-router-dom";
import OauthSection from "../components/OauthSection";
import RegisterCodeInput from "./RegisterCodeInput";
import RegisterProfile from "./RegisterProfile";
import { useRegister } from "../api/useRegister";
import { useCheckCode } from "../api/useCheckCode";

function Register() {
    const navigate = useNavigate();
    const { REGISTRATION_DISABLED, REGISTRATION_CODES_DISABLED } = useGameConfig();
    const [username, setUsername] = useState("");
    const [email, setEmail] = useState("");
    const [password, setPassword] = useState("");
    const [code, setCode] = useState("");
    const [showCodeInput, setShowCodeInput] = useState(!REGISTRATION_CODES_DISABLED);
    const [searchParams] = useSearchParams();
    const alphaKey = searchParams.get("alphaKey");

    // ORPC hooks
    const registerMutation = useRegister();
    const checkCodeMutation = useCheckCode();

    useEffect(() => {
        if (alphaKey) {
            setCode(alphaKey);
            setShowCodeInput(false);
        }
    }, [alphaKey]);

    if (REGISTRATION_DISABLED) {
        return <RegistrationDisabled />;
    }

    const checkCode = async (regCode: string) => {
        if (REGISTRATION_CODES_DISABLED) return true;

        try {
            const response = await checkCodeMutation.mutateAsync({ code: regCode });
            return !!response;
        } catch {
            toast.error("Invalid key!");
            return false;
        }
    };

    const registerSuccess = (method = "credentials") => {
        posthog.capture("user_registered", {
            $set: { username: username, email: email },
            $set_once: { code: code },
        });
        console.log("User registered");
        navigate(`/callback?auth=${method}`);
    };

    const register = async (e) => {
        e.preventDefault();
        if (username.length > 17) {
            toast.error("Student name is too long!");
            return;
        }
        if (username.length < 3) {
            toast.error("Student name is too short!");
            return;
        }
        if (password.length < 8) {
            toast.error("Please choose a longer password");
            return;
        }
        if (password.length > 26) {
            toast.error("Please choose a shorter password");
            return;
        }

        const userDetails: { email: string; password: string; username: string; code?: string } = {
            email: email,
            password: password,
            username: username,
        };

        if (!REGISTRATION_CODES_DISABLED) userDetails.code = code;

        try {
            // Use ORPC to register the user
            const response = await registerMutation.mutateAsync(userDetails);

            // Have to sign in to set session before auth redirect
            const { data } = await signIn.email({
                email,
                password,
            });

            if (data) {
                registerSuccess("credentials");
            }
            return response;
        } catch (error) {
            console.log(error);
            const result = await checkCode(code);
            if (!result) {
                toast.error("Email in use!");
            } else {
                toast.error("Server error! Try again later");
            }
        }
    };

    const handleKeyDownSecondForm = (e) => {
        if (e.key === "Enter") {
            e.preventDefault();
            register(e);
        }
    };

    if (showCodeInput) {
        return (
            <RegisterCodeInput
                setShowCodeInput={setShowCodeInput}
                code={code}
                setCode={setCode}
                checkCode={checkCode}
            />
        );
    }

    if (!username) return <RegisterProfile registerSuccess={registerSuccess} setUsername={setUsername} />;

    return (
        <div className="px-4 pt-3 pb-6 text-shadow shadow-sm sm:px-10 md:py-8">
            <form className="space-y-6" action="#" method="POST">
                <div>
                    <label htmlFor="email" className="block font-medium text-gray-300 text-sm">
                        Email address
                    </label>
                    <div className="mt-1">
                        <input
                            required
                            value={email}
                            id="email"
                            name="email"
                            type="email"
                            autoComplete="email"
                            className="block w-full appearance-none rounded-md border border-gray-600 bg-gray-800 px-3 py-2 text-gray-200 text-stroke-sm shadow-xs placeholder:text-gray-500 focus:border-indigo-500 focus:outline-hidden focus:ring-indigo-500 sm:text-sm"
                            onChange={(e) => {
                                setEmail(e.target.value);
                            }}
                        />
                    </div>
                </div>

                <div>
                    <label htmlFor="password" className="block font-medium text-gray-300 text-sm">
                        Password
                    </label>
                    <div className="mt-1">
                        <input
                            required
                            id="password"
                            name="password"
                            type="password"
                            autoComplete="current-password"
                            className="block w-full appearance-none rounded-md border border-gray-600 bg-gray-800 px-3 py-2 text-gray-200 shadow-xs placeholder:text-gray-500 focus:border-indigo-500 focus:outline-hidden focus:ring-indigo-500 sm:text-sm"
                            onKeyDown={(e) => handleKeyDownSecondForm(e)}
                            onChange={(e) => {
                                setPassword(e.target.value);
                            }}
                        />
                    </div>
                </div>

                {/* <TermsAndConditions /> */}
            </form>
            <div className="mt-6 flex w-full justify-center align-middle">
                <Button
                    variant="primary"
                    className="text-lg! font-medium! mx-auto w-4/5 text-stroke-sm uppercase"
                    onClick={(e) => register(e)}
                >
                    Register
                </Button>
            </div>
            <OauthSection />
            <div className="mt-4 text-center text-sm md:text-base">
                Already have an account?{" "}
                <Link className="text-blue-500 hover:brightness-125" to="/login">
                    Sign in
                </Link>
            </div>
        </div>
    );
}

// const TermsAndConditions = () => {
//   return (
//     <div className="flex items-center justify-between">
//       <div className="flex items-center">
//         <input
//           id="terms"
//           name="terms"
//           type="checkbox"
//           className="h-4 w-4 rounded-sm border-gray-300 text-sky-600 focus:ring-sky-500"
//         />
//         <label htmlFor="remember_me" className="ml-2 block text-sm text-gray-900">
//           I agree to the <Link className="text-slate-500">Terms and Conditions</Link>
//         </label>
//       </div>
//     </div>
//   );
// };
export default Register;
